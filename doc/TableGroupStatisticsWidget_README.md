# TableGroupStatisticsWidget 表格分组统计组件

## 概述

`TableGroupStatisticsWidget` 是一个强大的表格分组统计组件，基于 `PieChartWidget` 的设计模式创建。该组件支持按指定字段分组并对数值字段进行聚合统计，提供了灵活的配置方式和丰富的统计功能。

## 主要特性

- **多字段分组**: 支持按一个或多个字段进行分组
- **多种聚合函数**: 支持求和、计数、平均值、最大值、最小值、标准差、方差等聚合函数
- **小计和总计**: 支持显示分组小计和全局总计
- **数值格式化**: 支持自定义数值显示格式
- **灵活配置**: 提供多种配置方式，从简单到高级
- **自动配置**: 可根据数据集配置自动设置分组和统计字段

## 核心类结构

### TableGroupStatisticsWidget
主要的组件类，继承自 `BarChartWidget`，提供分组统计功能。

### TableGroupStatisticsData
内部数据结构类，用于存储分组统计结果：
- `columns`: 列定义映射
- `rows`: 分组统计行数据
- `dimsCodes`: 维度代码列表
- `groupConfig`: 分组配置信息

### GroupStatistic
分组统计配置类，用于配置统计字段和聚合函数。

## 使用方法

### 1. 基本使用

```java
TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();

// 设置分组字段
widget.addGroupByField("department");

// 添加统计字段
widget.addStatistic("sales_amount", "销售额", "sum");
widget.addStatistic("order_count", "订单数", "count");

// 设置显示选项
widget.setDisplayOptions(true, true); // 显示小计和总计
```

### 2. 多字段分组

```java
TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();

// 设置多个分组字段
widget.setGroupByFields(Arrays.asList("region", "department"));

// 添加多个统计字段
widget.addStatistic("sales_amount", "销售额", "sum");
widget.addStatistic("sales_amount", "平均销售额", "avg");
widget.addStatistic("order_count", "订单数", "count");
```

### 3. 高级配置

```java
TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();

// 创建详细的统计配置
GroupStatistic salesStat = new GroupStatistic("sales_amount", "销售总额", "sum");
salesStat.setDisplayFormat("#,##0.00"); // 设置数值格式
salesStat.setShowInSubtotal(true);
salesStat.setShowInGrandTotal(true);

widget.getGroupStatistics().add(salesStat);
```

### 4. 简化配置

```java
TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();

// 快速配置
widget.addGroupByField("product_category");
widget.addStatistic("revenue", "sum");
widget.addStatistic("quantity", "sum");
widget.setDisplayOptions(false, true); // 只显示总计
```

## 支持的聚合函数

| 函数名 | 说明 | 示例 |
|--------|------|------|
| sum | 求和 | `widget.addStatistic("amount", "sum")` |
| count | 计数 | `widget.addStatistic("order_id", "count")` |
| avg | 平均值 | `widget.addStatistic("score", "avg")` |
| max | 最大值 | `widget.addStatistic("price", "max")` |
| min | 最小值 | `widget.addStatistic("price", "min")` |
| std | 标准差 | `widget.addStatistic("variance_data", "std")` |
| variance | 方差 | `widget.addStatistic("variance_data", "variance")` |

## 配置选项

### 分组配置
- `groupByFields`: 分组字段列表
- `groupStatistics`: 统计配置列表

### 显示选项
- `showSubtotal`: 是否显示小计行
- `showGrandTotal`: 是否显示总计行

### 数值格式化
通过 `GroupStatistic.displayFormat` 属性设置数值显示格式：
- `#,##0.00`: 千分位分隔符，保留两位小数
- `#,##0`: 千分位分隔符，整数显示
- `0.00%`: 百分比格式

## API 方法

### 配置方法
- `addGroupByField(String fieldCode)`: 添加分组字段
- `addStatistic(String fieldCode, String fieldName, String aggregateFunction)`: 添加统计配置
- `addStatistic(String fieldCode, String aggregateFunction)`: 添加统计配置（简化版）
- `setDisplayOptions(boolean showSubtotal, boolean showGrandTotal)`: 设置显示选项
- `clearGroupConfiguration()`: 清空分组配置

### 获取方法
- `getGroupByFields()`: 获取分组字段列表
- `getGroupStatistics()`: 获取统计配置列表
- `getTableGroupStatisticsData()`: 获取统计结果数据

## 数据结构

### 输入数据
组件接收 `ColumnDataModel` 类型的数据，包含：
- 字段名列表
- 行数据列表

### 输出数据
组件返回包含以下结构的 Map：
```json
{
  "code": 1,
  "data": {
    "columns": {
      "department": "部门",
      "sales_amount_sum": "销售额(求和)",
      "order_count_count": "订单数(计数)"
    },
    "rows": [
      {
        "department": "销售部",
        "sales_amount_sum": "1000000.00",
        "order_count_count": 150
      },
      {
        "department": "总计",
        "sales_amount_sum": "5000000.00",
        "order_count_count": 750
      }
    ],
    "dimsCodes": ["部门"],
    "groupConfig": {}
  }
}
```

## 注意事项

1. **数据类型**: 统计字段必须是数值类型，非数值数据会被忽略
2. **NULL值处理**: NULL值在聚合计算中会被忽略
3. **内存使用**: 大量分组可能消耗较多内存，建议对结果集大小进行限制
4. **性能考虑**: 复杂的多字段分组和多统计字段可能影响性能

## 扩展功能

### 自定义聚合函数
可以通过重写 `getFunc` 方法添加自定义聚合函数：

```java
@Override
public IMeasureFunc getFunc(String code, int isDistinct) {
    if ("median".equals(code)) {
        return new MedianFunc(isDistinct != 0);
    }
    return super.getFunc(code, isDistinct);
}
```

### 自定义格式化
通过 `GroupStatistic.displayFormat` 属性自定义数值显示格式。

## 示例代码

详细的使用示例请参考：
- `TableGroupStatisticsExample.java` - 各种使用场景的示例代码

## 与其他组件的关系

- **继承关系**: 继承自 `BarChartWidget`
- **参考设计**: 基于 `PieChartWidget` 的设计模式
- **配合使用**: 可与 `GroupStatistic` 类配合使用
- **数据兼容**: 与现有的数据集配置系统兼容

## 版本信息

- **创建日期**: 2025-07-04
- **基于版本**: PieChartWidget 设计模式
- **组件类型**: WidgetType.TABLE_GROUP_STATISTICS
