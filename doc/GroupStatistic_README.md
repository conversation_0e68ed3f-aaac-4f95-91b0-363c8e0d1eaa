# GroupStatistic 分组统计配置类

## 概述

`GroupStatistic` 是表格分组统计功能的核心配置类，用于定义统计字段、聚合函数、显示格式等配置信息。该类为 `TableGroupStatisticsWidget` 提供了灵活的统计配置能力。

## 主要特性

- **字段配置**: 支持配置统计字段的代码和显示名称
- **聚合函数**: 支持多种聚合函数（sum, count, avg, max, min, std, variance）
- **数值格式化**: 支持自定义数值显示格式
- **显示控制**: 可控制统计结果在小计和总计中的显示
- **配置验证**: 提供配置有效性验证功能
- **对象操作**: 支持复制、比较等对象操作

## 类结构

### 核心属性

| 属性名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| fieldCode | String | 字段代码 | null |
| fieldName | String | 字段显示名称 | null |
| aggregateFunction | String | 聚合函数类型 | null |
| displayFormat | String | 数值显示格式 | null |
| showInSubtotal | boolean | 是否显示在小计行 | true |
| showInGrandTotal | boolean | 是否显示在总计行 | true |

### 构造函数

```java
// 默认构造函数
public GroupStatistic()

// 基本构造函数
public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction)

// 完整构造函数
public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction, String displayFormat)
```

## 支持的聚合函数

| 函数名 | 中文名称 | 说明 | 示例 |
|--------|----------|------|------|
| sum | 求和 | 计算数值字段的总和 | 销售额求和 |
| count | 计数 | 计算记录数量 | 订单数量统计 |
| avg | 平均值 | 计算数值字段的平均值 | 平均销售额 |
| max | 最大值 | 获取数值字段的最大值 | 最高销售额 |
| min | 最小值 | 获取数值字段的最小值 | 最低销售额 |
| std | 标准差 | 计算数值字段的标准差 | 销售额标准差 |
| variance | 方差 | 计算数值字段的方差 | 销售额方差 |

## 数值格式化

通过 `displayFormat` 属性可以设置数值的显示格式：

| 格式 | 说明 | 示例输出 |
|------|------|----------|
| `#,##0.00` | 千分位分隔符，保留两位小数 | 1,234.56 |
| `#,##0` | 千分位分隔符，整数显示 | 1,235 |
| `0.00%` | 百分比格式 | 12.34% |
| `¥#,##0.00` | 货币格式 | ¥1,234.56 |
| `#.##E0` | 科学计数法 | 1.23E3 |

## 使用方法

### 1. 基本使用

```java
// 创建销售额求和统计
GroupStatistic salesStat = new GroupStatistic("sales_amount", "销售额", "sum");

// 设置数值格式
salesStat.setDisplayFormat("#,##0.00");

// 配置显示选项
salesStat.setShowInSubtotal(true);
salesStat.setShowInGrandTotal(true);
```

### 2. 批量创建

```java
List<GroupStatistic> statistics = Arrays.asList(
    new GroupStatistic("sales_amount", "销售额", "sum", "#,##0.00"),
    new GroupStatistic("order_count", "订单数", "count"),
    new GroupStatistic("sales_amount", "平均销售额", "avg", "#,##0.00")
);
```

### 3. 高级配置

```java
// 创建平均值统计（不在总计中显示）
GroupStatistic avgStat = new GroupStatistic("sales_amount", "平均销售额", "avg", "#,##0.00");
avgStat.setShowInSubtotal(true);
avgStat.setShowInGrandTotal(false); // 平均值通常不在总计中显示
```

## API 方法

### 基本方法

```java
// 获取/设置字段代码
String getFieldCode()
void setFieldCode(String fieldCode)

// 获取/设置字段名称
String getFieldName()
void setFieldName(String fieldName)

// 获取/设置聚合函数
String getAggregateFunction()
void setAggregateFunction(String aggregateFunction)

// 获取/设置显示格式
String getDisplayFormat()
void setDisplayFormat(String displayFormat)

// 获取/设置显示选项
boolean isShowInSubtotal()
void setShowInSubtotal(boolean showInSubtotal)
boolean isShowInGrandTotal()
void setShowInGrandTotal(boolean showInGrandTotal)
```

### 便利方法

```java
// 验证配置是否有效
boolean isValid()

// 获取统计字段的唯一键
String getStatisticKey()

// 获取完整显示名称
String getDisplayName()

// 获取聚合函数的中文显示名称
String getAggregateFunctionDisplayName()

// 复制当前配置
GroupStatistic copy()
```

## 配置验证

使用 `isValid()` 方法可以验证配置是否有效：

```java
GroupStatistic stat = new GroupStatistic("sales", "销售额", "sum");
if (stat.isValid()) {
    // 配置有效，可以使用
    System.out.println("配置有效: " + stat.getDisplayName());
} else {
    // 配置无效，需要修正
    System.out.println("配置无效，请检查必填字段");
}
```

**验证规则**:
- `fieldCode` 不能为 null 或空字符串
- `fieldName` 不能为 null 或空字符串  
- `aggregateFunction` 不能为 null 或空字符串

## 对象操作

### 复制对象

```java
GroupStatistic original = new GroupStatistic("sales", "销售额", "sum");
GroupStatistic copy = original.copy();

// 修改复制对象不会影响原对象
copy.setFieldName("修改后的销售额");
```

### 比较对象

```java
GroupStatistic stat1 = new GroupStatistic("sales", "销售额", "sum");
GroupStatistic stat2 = new GroupStatistic("sales", "销售额", "sum");

// 内容相同的对象相等
boolean isEqual = stat1.equals(stat2); // true
int hash1 = stat1.hashCode();
int hash2 = stat2.hashCode(); // hash1 == hash2
```

## 最佳实践

### 1. 字段命名
- 使用有意义的字段代码，如 `sales_amount` 而不是 `field1`
- 字段名称使用中文，便于用户理解

### 2. 聚合函数选择
- 数值字段通常使用 `sum`、`avg`、`max`、`min`
- 计数统计使用 `count`
- 统计分析使用 `std`、`variance`

### 3. 格式化设置
- 金额字段使用千分位分隔符：`#,##0.00`
- 百分比字段使用百分比格式：`0.00%`
- 整数字段使用：`#,##0`

### 4. 显示控制
- 平均值、标准差等通常不在总计中显示
- 求和、计数等通常在小计和总计中都显示

### 5. 配置验证
- 创建配置后务必调用 `isValid()` 验证
- 在批量创建时过滤无效配置

## 示例代码

详细的使用示例请参考：
- `GroupStatisticExample.java` - 各种使用场景的示例代码
- `GroupStatisticTest.java` - 完整的单元测试

## 与其他类的关系

- **TableGroupStatisticsWidget**: 使用 GroupStatistic 配置统计字段
- **TableGroupStatisticsData**: 存储基于 GroupStatistic 配置生成的统计结果
- **WidgetDatasetMeasures**: 可以从数据集度量配置自动创建 GroupStatistic

## 扩展建议

1. **自定义聚合函数**: 扩展支持更多统计函数
2. **条件统计**: 支持带条件的统计计算
3. **多字段统计**: 支持跨字段的复合统计
4. **动态格式**: 根据数值大小动态选择格式
5. **国际化**: 支持多语言的函数名称显示

## 注意事项

1. **线程安全**: 该类不是线程安全的，多线程环境下需要注意同步
2. **内存使用**: 大量创建对象时注意内存使用
3. **格式化性能**: 复杂的数值格式化可能影响性能
4. **空值处理**: 聚合计算时会自动过滤空值
