import com.dragonsoft.cicada.datacenter.modules.datavisual.model.GroupStatistic;

/**
 * Simple test for GroupStatistic class
 */
public class TestGroupStatistic {
    
    public static void main(String[] args) {
        System.out.println("Testing GroupStatistic class...");
        
        // Test default constructor
        GroupStatistic stat1 = new GroupStatistic();
        System.out.println("Default constructor: " + stat1.toString());
        
        // Test parameterized constructor
        GroupStatistic stat2 = new GroupStatistic("amount", "Amount Field", "sum");
        System.out.println("Parameterized constructor: " + stat2.toString());
        
        // Test full constructor
        GroupStatistic stat3 = new GroupStatistic("price", "Price Field", "avg", "#,##0.00");
        stat3.setShowInSubtotal(false);
        stat3.setShowInGrandTotal(false);
        System.out.println("Full constructor: " + stat3.toString());
        
        // Test validation
        System.out.println("stat1 is valid: " + stat1.isValid());
        System.out.println("stat2 is valid: " + stat2.isValid());
        System.out.println("stat3 is valid: " + stat3.isValid());
        
        // Test utility methods
        System.out.println("stat2 statistic key: " + stat2.getStatisticKey());
        System.out.println("stat2 display name: " + stat2.getDisplayName());
        System.out.println("stat3 aggregate function display: " + stat3.getAggregateFunctionDisplayName());
        
        // Test copy
        GroupStatistic stat4 = stat3.copy();
        System.out.println("Copied stat3: " + stat4.toString());
        System.out.println("stat3 equals stat4: " + stat3.equals(stat4));
        System.out.println("stat3 hashCode == stat4 hashCode: " + (stat3.hashCode() == stat4.hashCode()));
        
        // Test different aggregate functions
        String[] functions = {"sum", "count", "avg", "max", "min", "std", "variance", "unknown"};
        for (String func : functions) {
            GroupStatistic temp = new GroupStatistic("test", "Test", func);
            System.out.println(func + " display name: " + temp.getAggregateFunctionDisplayName());
        }
        
        System.out.println("All tests completed successfully!");
    }
}
