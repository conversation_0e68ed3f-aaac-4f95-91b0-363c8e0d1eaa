package com.dragonsoft.cicada.datacenter.common.config;


import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Properties;
import java.util.Set;

@Component
public class MlSqlEngineConfig {


    private List<String> urlList = Lists.newArrayList();

    @PostConstruct
    public void init() throws IOException {
        Properties properties = new Properties();
        properties.load(this.getClass().getResourceAsStream("/mlsql-engine-config.properties"));
        Set<String> set = properties.stringPropertyNames();
        for (String key : set) {
            String value = properties.getProperty(key);
            urlList.add(value);
        }
    }


    public List<String> getUrlList() {
        return urlList;
    }
}
