package com.dragonsoft.cicada.datacenter.modules.datavisual.model.example;

import com.dragonsoft.cicada.datacenter.modules.datavisual.model.GroupStatistic;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.TableGroupStatisticsWidget;

import java.util.Arrays;

/**
 * 表格分组统计组件使用示例
 * 
 * <AUTHOR> Generated
 */
public class TableGroupStatisticsExample {

    /**
     * 基本使用示例
     * 按部门分组，统计销售额和订单数
     */
    public static void basicUsageExample() {
        TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();
        
        // 设置分组字段
        widget.addGroupByField("department");
        
        // 添加统计字段
        widget.addStatistic("sales_amount", "销售额", "sum");
        widget.addStatistic("order_count", "订单数", "count");
        
        // 设置显示选项
        widget.setDisplayOptions(true, true); // 显示小计和总计
        
        System.out.println("基本分组统计配置完成");
    }

    /**
     * 多字段分组示例
     * 按地区和部门分组，统计多个指标
     */
    public static void multiFieldGroupingExample() {
        TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();
        
        // 设置多个分组字段
        widget.setGroupByFields(Arrays.asList("region", "department"));
        
        // 添加多个统计字段
        widget.addStatistic("sales_amount", "销售额", "sum");
        widget.addStatistic("sales_amount", "平均销售额", "avg");
        widget.addStatistic("order_count", "订单数", "count");
        widget.addStatistic("customer_count", "客户数", "count");
        
        // 设置显示选项
        widget.setShowSubtotal(true);
        widget.setShowGrandTotal(true);
        
        System.out.println("多字段分组统计配置完成");
    }

    /**
     * 高级配置示例
     * 使用GroupStatistic对象进行详细配置
     */
    public static void advancedConfigurationExample() {
        TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();
        
        // 设置分组字段
        widget.addGroupByField("category");
        widget.addGroupByField("subcategory");
        
        // 创建详细的统计配置
        GroupStatistic salesSumStat = new GroupStatistic("sales_amount", "销售总额", "sum");
        salesSumStat.setDisplayFormat("#,##0.00"); // 设置数值格式
        salesSumStat.setShowInSubtotal(true);
        salesSumStat.setShowInGrandTotal(true);
        
        GroupStatistic salesAvgStat = new GroupStatistic("sales_amount", "平均销售额", "avg");
        salesAvgStat.setDisplayFormat("#,##0.00");
        salesAvgStat.setShowInSubtotal(true);
        salesAvgStat.setShowInGrandTotal(false); // 总计行不显示平均值
        
        GroupStatistic orderCountStat = new GroupStatistic("order_id", "订单数量", "count");
        orderCountStat.setShowInSubtotal(true);
        orderCountStat.setShowInGrandTotal(true);
        
        // 添加统计配置
        widget.getGroupStatistics().add(salesSumStat);
        widget.getGroupStatistics().add(salesAvgStat);
        widget.getGroupStatistics().add(orderCountStat);
        
        // 设置显示选项
        widget.setDisplayOptions(true, true);
        
        System.out.println("高级配置完成");
    }

    /**
     * 简化配置示例
     * 使用便利方法快速配置
     */
    public static void simplifiedConfigurationExample() {
        TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();
        
        // 快速添加分组字段
        widget.addGroupByField("product_category");
        
        // 快速添加统计字段（使用默认字段名）
        widget.addStatistic("revenue", "sum");
        widget.addStatistic("quantity", "sum");
        widget.addStatistic("profit", "avg");
        
        // 只显示总计，不显示小计
        widget.setDisplayOptions(false, true);
        
        System.out.println("简化配置完成");
    }

    /**
     * 清空和重新配置示例
     */
    public static void reconfigurationExample() {
        TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();
        
        // 初始配置
        widget.addGroupByField("region");
        widget.addStatistic("sales", "sum");
        
        System.out.println("初始配置: " + widget.getGroupByFields().size() + " 个分组字段, " 
                          + widget.getGroupStatistics().size() + " 个统计字段");
        
        // 清空配置
        widget.clearGroupConfiguration();
        
        // 重新配置
        widget.addGroupByField("department");
        widget.addGroupByField("team");
        widget.addStatistic("performance_score", "绩效得分", "avg");
        widget.addStatistic("task_count", "任务数量", "count");
        
        System.out.println("重新配置: " + widget.getGroupByFields().size() + " 个分组字段, " 
                          + widget.getGroupStatistics().size() + " 个统计字段");
    }

    /**
     * 支持的聚合函数示例
     */
    public static void aggregateFunctionsExample() {
        TableGroupStatisticsWidget widget = new TableGroupStatisticsWidget();
        
        widget.addGroupByField("category");
        
        // 演示所有支持的聚合函数
        widget.addStatistic("amount", "总和", "sum");
        widget.addStatistic("amount", "计数", "count");
        widget.addStatistic("amount", "平均值", "avg");
        widget.addStatistic("amount", "最大值", "max");
        widget.addStatistic("amount", "最小值", "min");
        widget.addStatistic("amount", "标准差", "std");
        widget.addStatistic("amount", "方差", "variance");
        
        System.out.println("聚合函数示例配置完成，共 " + widget.getGroupStatistics().size() + " 个统计字段");
    }

    /**
     * 运行所有示例
     */
    public static void main(String[] args) {
        System.out.println("=== 表格分组统计组件使用示例 ===\n");
        
        System.out.println("1. 基本使用示例:");
        basicUsageExample();
        System.out.println();
        
        System.out.println("2. 多字段分组示例:");
        multiFieldGroupingExample();
        System.out.println();
        
        System.out.println("3. 高级配置示例:");
        advancedConfigurationExample();
        System.out.println();
        
        System.out.println("4. 简化配置示例:");
        simplifiedConfigurationExample();
        System.out.println();
        
        System.out.println("5. 重新配置示例:");
        reconfigurationExample();
        System.out.println();
        
        System.out.println("6. 聚合函数示例:");
        aggregateFunctionsExample();
        System.out.println();
        
        System.out.println("=== 所有示例运行完成 ===");
    }
}
