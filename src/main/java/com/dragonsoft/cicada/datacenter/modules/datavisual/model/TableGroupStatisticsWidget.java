package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Table Group Statistics Widget
 * Supports grouping by specified fields and aggregating numeric fields
 * 
 * <AUTHOR> Generated
 */
@Slf4j
@WidgetLabel(name = "表格分组统计", type = WidgetType.TABLE_GROUP_STATISTICS, describe = "支持分组统计的表格组件")
public class TableGroupStatisticsWidget extends BarChartWidget {

    /**
     * Table group statistics data structure
     */
    protected TableGroupStatisticsData tableGroupStatisticsData = new TableGroupStatisticsData();
    
    /**
     * Group by fields list
     */
    private List<String> groupByFields = new ArrayList<>();
    
    /**
     * Group statistics configuration list
     */
    private List<GroupStatistic> groupStatistics = new ArrayList<>();
    
    /**
     * Whether to show subtotal
     */
    private boolean showSubtotal = true;
    
    /**
     * Whether to show grand total
     */
    private boolean showGrandTotal = true;

    @Override
    public Map<String, Object> builderResult(ColumnDataModel columns, String code) {
        log.info("Start building table group statistics data, code: {}", code);
        
        // Initialize grouping configuration
        initializeGroupingConfiguration();
        
        // Build grouped statistics data
        return buildGroupedStatisticsResult(columns);
    }

    /**
     * Initialize grouping configuration
     * Automatically set group fields and statistics fields based on dataset configuration
     */
    private void initializeGroupingConfiguration() {
        // Get dimension fields as group fields
        Set<WidgetDatasetDims> dims = this.widgetDataset.getWidgetDatasetDims();
        if (!dims.isEmpty()) {
            this.groupByFields = dims.stream()
                    .map(WidgetDatasetDims::getFiledCode)
                    .collect(Collectors.toList());
        }
        
        // Get measure fields as statistics fields
        Set<WidgetDatasetMeasures> measures = this.widgetDataset.getWidgetDatasetMeasures();
        if (!measures.isEmpty()) {
            this.groupStatistics = measures.stream()
                    .map(measure -> new GroupStatistic(
                            measure.getFiledCode(),
                            measure.getFiledName(),
                            getDefaultAggregateFunction(measure.getFuncType())
                    ))
                    .collect(Collectors.toList());
        }
        
        log.info("Group fields: {}, Statistics fields: {}", groupByFields, groupStatistics.size());
    }

    /**
     * Get default aggregate function based on measure field function type
     */
    private String getDefaultAggregateFunction(String funcType) {
        if (funcType == null) {
            return "sum";
        }
        switch (funcType.toLowerCase()) {
            case "count":
                return "count";
            case "avg":
            case "mean":
                return "avg";
            case "max":
                return "max";
            case "min":
                return "min";
            case "sum":
            default:
                return "sum";
        }
    }

    /**
     * Build grouped statistics result
     */
    private Map<String, Object> buildGroupedStatisticsResult(ColumnDataModel columns) {
        Map<String, Object> result = Maps.newHashMap();
        
        try {
            // Build column definitions
            buildColumnDefinitions();
            
            // Perform group statistics
            performGroupStatistics(columns);
            
            result.put("code", 1);
            result.put("data", tableGroupStatisticsData);
            
            log.info("Table group statistics data build completed, group count: {}", tableGroupStatisticsData.getRows().size());
            
        } catch (Exception e) {
            log.error("Error occurred while building table group statistics data", e);
            result.put("code", -1);
            result.put("message", "Data processing failed: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * Build column definitions
     */
    private void buildColumnDefinitions() {
        List<String> columns = Lists.newArrayList();
        
        // Add group field columns
        for (String groupField : groupByFields) {
            String fieldName = getFieldDisplayName(groupField);
            columns.add(fieldName);
            tableGroupStatisticsData.getColumns().put(groupField, fieldName);
        }
        
        // Add statistics field columns
        for (GroupStatistic statistic : groupStatistics) {
            String columnName = statistic.getFieldName() + "(" + getAggregateFunctionDisplayName(statistic.getAggregateFunction()) + ")";
            columns.add(columnName);
            tableGroupStatisticsData.getColumns().put(statistic.getFieldCode() + "_" + statistic.getAggregateFunction(), columnName);
        }
        
        // Set dimension codes list
        List<String> dimsCodes = this.widgetDataset.getFromListDims().stream()
                .map(WidgetDatasetDims::getFiledName)
                .collect(Collectors.toList());
        tableGroupStatisticsData.setDimsCodes(dimsCodes);
    }

    /**
     * Get field display name
     */
    private String getFieldDisplayName(String fieldCode) {
        // Find field display name from dimension configuration
        return this.widgetDataset.getWidgetDatasetDims().stream()
                .filter(dim -> dim.getFiledCode().equals(fieldCode))
                .map(WidgetDatasetDims::getFiledName)
                .findFirst()
                .orElse(fieldCode);
    }

    /**
     * Get aggregate function display name
     */
    private String getAggregateFunctionDisplayName(String function) {
        switch (function.toLowerCase()) {
            case "sum":
                return "Sum";
            case "count":
                return "Count";
            case "avg":
                return "Average";
            case "max":
                return "Maximum";
            case "min":
                return "Minimum";
            case "std":
                return "Standard Deviation";
            case "variance":
                return "Variance";
            default:
                return function;
        }
    }

    /**
     * Perform group statistics
     */
    private void performGroupStatistics(ColumnDataModel columns) {
        if (columns == null || columns.getFieldValue().isEmpty()) {
            log.warn("Data is empty, cannot perform group statistics");
            return;
        }
        
        // Group data by group fields
        Map<String, List<Map>> groupedData = groupDataByFields(columns.getFieldValue());
        
        // Calculate statistics for each group
        List<Map<String, Object>> groupedRows = Lists.newArrayList();
        Map<String, BigDecimal> grandTotalValues = Maps.newHashMap();
        
        for (Map.Entry<String, List<Map>> entry : groupedData.entrySet()) {
            String groupKey = entry.getKey();
            List<Map> groupRows = entry.getValue();
            
            // Build group row data
            Map<String, Object> groupRowData = buildGroupRowData(groupKey, groupRows, grandTotalValues);
            groupedRows.add(groupRowData);
        }
        
        // Add grand total row
        if (showGrandTotal && !grandTotalValues.isEmpty()) {
            Map<String, Object> grandTotalRow = buildGrandTotalRow(grandTotalValues);
            groupedRows.add(grandTotalRow);
        }
        
        tableGroupStatisticsData.setRows(groupedRows);
    }

    /**
     * Group data by group fields
     */
    private Map<String, List<Map>> groupDataByFields(List<Map> data) {
        Map<String, List<Map>> groupedData = Maps.newLinkedHashMap();
        
        for (Map row : data) {
            // Build group key
            String groupKey = buildGroupKey(row);
            
            // Add row data to corresponding group
            groupedData.computeIfAbsent(groupKey, k -> Lists.newArrayList()).add(row);
        }
        
        return groupedData;
    }

    /**
     * Build group key
     */
    private String buildGroupKey(Map row) {
        return groupByFields.stream()
                .map(field -> String.valueOf(row.get(field)))
                .collect(Collectors.joining("||"));
    }

    /**
     * Build group row data
     */
    private Map<String, Object> buildGroupRowData(String groupKey, List<Map> groupRows, Map<String, BigDecimal> grandTotalValues) {
        Map<String, Object> rowData = Maps.newLinkedHashMap();
        
        // Parse group key and set group field values
        String[] groupValues = groupKey.split("\\|\\|");
        for (int i = 0; i < groupByFields.size() && i < groupValues.length; i++) {
            rowData.put(groupByFields.get(i), groupValues[i]);
        }
        
        // Calculate statistics values
        for (GroupStatistic statistic : groupStatistics) {
            BigDecimal statisticValue = calculateStatisticValue(groupRows, statistic);
            String statisticKey = statistic.getFieldCode() + "_" + statistic.getAggregateFunction();
            
            // Format numeric value
            Object formattedValue = formatStatisticValue(statisticValue, statistic);
            rowData.put(statisticKey, formattedValue);
            
            // Add to grand total
            if (showGrandTotal) {
                grandTotalValues.merge(statisticKey, statisticValue, BigDecimal::add);
            }
        }
        
        return rowData;
    }

    /**
     * Calculate statistic value
     */
    private BigDecimal calculateStatisticValue(List<Map> rows, GroupStatistic statistic) {
        String fieldCode = statistic.getFieldCode();
        String function = statistic.getAggregateFunction().toLowerCase();
        
        List<BigDecimal> values = rows.stream()
                .map(row -> convertToBigDecimal(row.get(fieldCode)))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        if (values.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        switch (function) {
            case "sum":
                return values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            case "count":
                return BigDecimal.valueOf(values.size());
            case "avg":
                BigDecimal sum = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.divide(BigDecimal.valueOf(values.size()), 2, RoundingMode.HALF_UP);
            case "max":
                return values.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            case "min":
                return values.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            default:
                return values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }
