package com.dragonsoft.cicada.datacenter.modules.datavisual.model.example;

import com.dragonsoft.cicada.datacenter.modules.datavisual.model.GroupStatistic;

import java.util.ArrayList;
import java.util.List;

/**
 * GroupStatistic 使用示例
 * 展示如何创建和配置分组统计对象
 * 
 * <AUTHOR> Generated
 */
public class GroupStatisticExample {

    /**
     * 基本使用示例
     */
    public static void basicUsageExample() {
        System.out.println("=== 基本使用示例 ===");
        
        // 使用默认构造函数
        GroupStatistic stat1 = new GroupStatistic();
        stat1.setFieldCode("sales_amount");
        stat1.setFieldName("销售额");
        stat1.setAggregateFunction("sum");
        
        System.out.println("统计配置1: " + stat1);
        System.out.println("统计键: " + stat1.getStatisticKey());
        System.out.println("显示名称: " + stat1.getDisplayName());
        System.out.println("是否有效: " + stat1.isValid());
        System.out.println();
        
        // 使用基本构造函数
        GroupStatistic stat2 = new GroupStatistic("order_count", "订单数量", "count");
        System.out.println("统计配置2: " + stat2);
        System.out.println("统计键: " + stat2.getStatisticKey());
        System.out.println("显示名称: " + stat2.getDisplayName());
        System.out.println();
    }

    /**
     * 高级配置示例
     */
    public static void advancedConfigurationExample() {
        System.out.println("=== 高级配置示例 ===");
        
        // 创建带格式化的统计配置
        GroupStatistic salesStat = new GroupStatistic("sales_amount", "销售总额", "sum", "#,##0.00");
        salesStat.setShowInSubtotal(true);
        salesStat.setShowInGrandTotal(true);
        
        System.out.println("销售额统计: " + salesStat);
        System.out.println("显示格式: " + salesStat.getDisplayFormat());
        System.out.println();
        
        // 创建平均值统计（不在总计中显示）
        GroupStatistic avgStat = new GroupStatistic("sales_amount", "平均销售额", "avg", "#,##0.00");
        avgStat.setShowInSubtotal(true);
        avgStat.setShowInGrandTotal(false); // 平均值通常不在总计中显示
        
        System.out.println("平均值统计: " + avgStat);
        System.out.println("显示在小计: " + avgStat.isShowInSubtotal());
        System.out.println("显示在总计: " + avgStat.isShowInGrandTotal());
        System.out.println();
    }

    /**
     * 所有聚合函数示例
     */
    public static void allAggregateFunctionsExample() {
        System.out.println("=== 所有聚合函数示例 ===");
        
        String[] functions = {"sum", "count", "avg", "max", "min", "std", "variance"};
        String fieldCode = "amount";
        String fieldName = "金额";
        
        for (String function : functions) {
            GroupStatistic stat = new GroupStatistic(fieldCode, fieldName, function);
            System.out.println(function.toUpperCase() + " 函数:");
            System.out.println("  显示名称: " + stat.getDisplayName());
            System.out.println("  中文名称: " + stat.getAggregateFunctionDisplayName());
            System.out.println("  统计键: " + stat.getStatisticKey());
            System.out.println();
        }
    }

    /**
     * 数值格式化示例
     */
    public static void numberFormattingExample() {
        System.out.println("=== 数值格式化示例 ===");
        
        // 不同的数值格式
        String[][] formats = {
            {"#,##0.00", "千分位分隔符，保留两位小数"},
            {"#,##0", "千分位分隔符，整数显示"},
            {"0.00%", "百分比格式"},
            {"¥#,##0.00", "货币格式"},
            {"#.##E0", "科学计数法"}
        };
        
        for (String[] format : formats) {
            GroupStatistic stat = new GroupStatistic("amount", "金额", "sum", format[0]);
            System.out.println("格式: " + format[0]);
            System.out.println("说明: " + format[1]);
            System.out.println("配置: " + stat.getDisplayFormat());
            System.out.println();
        }
    }

    /**
     * 批量创建统计配置示例
     */
    public static void batchCreationExample() {
        System.out.println("=== 批量创建统计配置示例 ===");
        
        List<GroupStatistic> statistics = new ArrayList<>();
        
        // 销售相关统计
        statistics.add(new GroupStatistic("sales_amount", "销售额", "sum", "#,##0.00"));
        statistics.add(new GroupStatistic("sales_amount", "平均销售额", "avg", "#,##0.00"));
        statistics.add(new GroupStatistic("sales_amount", "最高销售额", "max", "#,##0.00"));
        statistics.add(new GroupStatistic("sales_amount", "最低销售额", "min", "#,##0.00"));
        
        // 订单相关统计
        statistics.add(new GroupStatistic("order_count", "订单数量", "count"));
        statistics.add(new GroupStatistic("customer_count", "客户数量", "count"));
        
        // 利润相关统计
        GroupStatistic profitStat = new GroupStatistic("profit", "利润", "sum", "#,##0.00");
        profitStat.setShowInSubtotal(true);
        profitStat.setShowInGrandTotal(true);
        statistics.add(profitStat);
        
        System.out.println("创建了 " + statistics.size() + " 个统计配置:");
        for (int i = 0; i < statistics.size(); i++) {
            GroupStatistic stat = statistics.get(i);
            System.out.println((i + 1) + ". " + stat.getDisplayName() + " [" + stat.getStatisticKey() + "]");
        }
        System.out.println();
    }

    /**
     * 配置验证示例
     */
    public static void validationExample() {
        System.out.println("=== 配置验证示例 ===");
        
        // 有效配置
        GroupStatistic validStat = new GroupStatistic("sales", "销售额", "sum");
        System.out.println("有效配置: " + validStat.isValid() + " - " + validStat);
        
        // 无效配置示例
        GroupStatistic[] invalidStats = {
            new GroupStatistic(null, "销售额", "sum"),
            new GroupStatistic("sales", null, "sum"),
            new GroupStatistic("sales", "销售额", null),
            new GroupStatistic("", "销售额", "sum"),
            new GroupStatistic("sales", "", "sum"),
            new GroupStatistic("sales", "销售额", ""),
            new GroupStatistic("   ", "销售额", "sum")
        };
        
        for (int i = 0; i < invalidStats.length; i++) {
            GroupStatistic stat = invalidStats[i];
            System.out.println("无效配置" + (i + 1) + ": " + stat.isValid() + " - " + stat);
        }
        System.out.println();
    }

    /**
     * 对象复制示例
     */
    public static void copyExample() {
        System.out.println("=== 对象复制示例 ===");
        
        // 创建原始对象
        GroupStatistic original = new GroupStatistic("sales_amount", "销售额", "sum", "#,##0.00");
        original.setShowInSubtotal(false);
        original.setShowInGrandTotal(true);
        
        System.out.println("原始对象: " + original);
        
        // 复制对象
        GroupStatistic copy = original.copy();
        System.out.println("复制对象: " + copy);
        
        // 验证是否为不同实例
        System.out.println("是否为同一实例: " + (original == copy));
        System.out.println("内容是否相等: " + original.equals(copy));
        
        // 修改复制对象
        copy.setFieldName("修改后的销售额");
        copy.setAggregateFunction("avg");
        
        System.out.println("修改后:");
        System.out.println("原始对象: " + original.getDisplayName());
        System.out.println("复制对象: " + copy.getDisplayName());
        System.out.println();
    }

    /**
     * 对象比较示例
     */
    public static void comparisonExample() {
        System.out.println("=== 对象比较示例 ===");
        
        GroupStatistic stat1 = new GroupStatistic("sales", "销售额", "sum");
        GroupStatistic stat2 = new GroupStatistic("sales", "销售额", "sum");
        GroupStatistic stat3 = new GroupStatistic("sales", "销售额", "avg");
        
        System.out.println("stat1: " + stat1.getDisplayName());
        System.out.println("stat2: " + stat2.getDisplayName());
        System.out.println("stat3: " + stat3.getDisplayName());
        
        System.out.println("stat1.equals(stat2): " + stat1.equals(stat2));
        System.out.println("stat1.equals(stat3): " + stat1.equals(stat3));
        System.out.println("stat1.hashCode() == stat2.hashCode(): " + (stat1.hashCode() == stat2.hashCode()));
        System.out.println("stat1.hashCode() == stat3.hashCode(): " + (stat1.hashCode() == stat3.hashCode()));
        System.out.println();
    }

    /**
     * 运行所有示例
     */
    public static void main(String[] args) {
        System.out.println("GroupStatistic 使用示例\n");
        
        basicUsageExample();
        advancedConfigurationExample();
        allAggregateFunctionsExample();
        numberFormattingExample();
        batchCreationExample();
        validationExample();
        copyExample();
        comparisonExample();
        
        System.out.println("所有示例运行完成！");
    }
}
