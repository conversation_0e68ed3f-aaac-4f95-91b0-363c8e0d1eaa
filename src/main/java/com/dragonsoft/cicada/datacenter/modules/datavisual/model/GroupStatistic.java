package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

/**
 * 分组统计配置类
 * 用于配置表格分组统计的字段和聚合函数
 * 
 * <AUTHOR> Generated
 */
public class GroupStatistic {
    
    /**
     * 字段代码
     */
    private String fieldCode;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 聚合函数类型
     * 支持: sum, count, avg, max, min, std, variance
     */
    private String aggregateFunction;
    
    /**
     * 显示格式
     * 例如: #,##0.00 用于数值格式化
     */
    private String displayFormat;
    
    /**
     * 是否显示在小计行
     */
    private boolean showInSubtotal = true;
    
    /**
     * 是否显示在总计行
     */
    private boolean showInGrandTotal = true;

    /**
     * 默认构造函数
     */
    public GroupStatistic() {
    }

    /**
     * 基本构造函数
     * 
     * @param fieldCode 字段代码
     * @param fieldName 字段名称
     * @param aggregateFunction 聚合函数
     */
    public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.aggregateFunction = aggregateFunction;
    }

    /**
     * 完整构造函数
     * 
     * @param fieldCode 字段代码
     * @param fieldName 字段名称
     * @param aggregateFunction 聚合函数
     * @param displayFormat 显示格式
     */
    public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction, String displayFormat) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.aggregateFunction = aggregateFunction;
        this.displayFormat = displayFormat;
    }

    /**
     * 获取字段代码
     * 
     * @return 字段代码
     */
    public String getFieldCode() {
        return fieldCode;
    }

    /**
     * 设置字段代码
     * 
     * @param fieldCode 字段代码
     */
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    /**
     * 获取字段名称
     * 
     * @return 字段名称
     */
    public String getFieldName() {
        return fieldName;
    }

    /**
     * 设置字段名称
     * 
     * @param fieldName 字段名称
     */
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    /**
     * 获取聚合函数
     * 
     * @return 聚合函数
     */
    public String getAggregateFunction() {
        return aggregateFunction;
    }

    /**
     * 设置聚合函数
     * 
     * @param aggregateFunction 聚合函数
     */
    public void setAggregateFunction(String aggregateFunction) {
        this.aggregateFunction = aggregateFunction;
    }

    /**
     * 获取显示格式
     * 
     * @return 显示格式
     */
    public String getDisplayFormat() {
        return displayFormat;
    }

    /**
     * 设置显示格式
     * 
     * @param displayFormat 显示格式
     */
    public void setDisplayFormat(String displayFormat) {
        this.displayFormat = displayFormat;
    }

    /**
     * 是否显示在小计行
     * 
     * @return true表示显示，false表示不显示
     */
    public boolean isShowInSubtotal() {
        return showInSubtotal;
    }

    /**
     * 设置是否显示在小计行
     * 
     * @param showInSubtotal true表示显示，false表示不显示
     */
    public void setShowInSubtotal(boolean showInSubtotal) {
        this.showInSubtotal = showInSubtotal;
    }

    /**
     * 是否显示在总计行
     * 
     * @return true表示显示，false表示不显示
     */
    public boolean isShowInGrandTotal() {
        return showInGrandTotal;
    }

    /**
     * 设置是否显示在总计行
     * 
     * @param showInGrandTotal true表示显示，false表示不显示
     */
    public void setShowInGrandTotal(boolean showInGrandTotal) {
        this.showInGrandTotal = showInGrandTotal;
    }

    /**
     * 验证配置是否有效
     * 
     * @return true表示配置有效，false表示配置无效
     */
    public boolean isValid() {
        return fieldCode != null && !fieldCode.trim().isEmpty() &&
               fieldName != null && !fieldName.trim().isEmpty() &&
               aggregateFunction != null && !aggregateFunction.trim().isEmpty();
    }

    /**
     * 获取统计字段的唯一键
     * 用于在结果数据中标识该统计字段
     * 
     * @return 统计字段的唯一键
     */
    public String getStatisticKey() {
        return fieldCode + "_" + aggregateFunction;
    }

    /**
     * 获取显示名称
     * 包含字段名称和聚合函数的完整显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return fieldName + "(" + getAggregateFunctionDisplayName() + ")";
    }

    /**
     * 获取聚合函数的显示名称
     * 
     * @return 聚合函数的中文显示名称
     */
    public String getAggregateFunctionDisplayName() {
        if (aggregateFunction == null) {
            return "";
        }
        switch (aggregateFunction.toLowerCase()) {
            case "sum":
                return "求和";
            case "count":
                return "计数";
            case "avg":
                return "平均值";
            case "max":
                return "最大值";
            case "min":
                return "最小值";
            case "std":
                return "标准差";
            case "variance":
                return "方差";
            default:
                return aggregateFunction;
        }
    }

    /**
     * 复制当前配置
     * 
     * @return 当前配置的副本
     */
    public GroupStatistic copy() {
        GroupStatistic copy = new GroupStatistic(this.fieldCode, this.fieldName, this.aggregateFunction, this.displayFormat);
        copy.setShowInSubtotal(this.showInSubtotal);
        copy.setShowInGrandTotal(this.showInGrandTotal);
        return copy;
    }

    @Override
    public String toString() {
        return "GroupStatistic{" +
                "fieldCode='" + fieldCode + '\'' +
                ", fieldName='" + fieldName + '\'' +
                ", aggregateFunction='" + aggregateFunction + '\'' +
                ", displayFormat='" + displayFormat + '\'' +
                ", showInSubtotal=" + showInSubtotal +
                ", showInGrandTotal=" + showInGrandTotal +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GroupStatistic that = (GroupStatistic) o;

        if (showInSubtotal != that.showInSubtotal) return false;
        if (showInGrandTotal != that.showInGrandTotal) return false;
        if (fieldCode != null ? !fieldCode.equals(that.fieldCode) : that.fieldCode != null) return false;
        if (fieldName != null ? !fieldName.equals(that.fieldName) : that.fieldName != null) return false;
        if (aggregateFunction != null ? !aggregateFunction.equals(that.aggregateFunction) : that.aggregateFunction != null)
            return false;
        return displayFormat != null ? displayFormat.equals(that.displayFormat) : that.displayFormat == null;
    }

    @Override
    public int hashCode() {
        int result = fieldCode != null ? fieldCode.hashCode() : 0;
        result = 31 * result + (fieldName != null ? fieldName.hashCode() : 0);
        result = 31 * result + (aggregateFunction != null ? aggregateFunction.hashCode() : 0);
        result = 31 * result + (displayFormat != null ? displayFormat.hashCode() : 0);
        result = 31 * result + (showInSubtotal ? 1 : 0);
        result = 31 * result + (showInGrandTotal ? 1 : 0);
        return result;
    }
}
