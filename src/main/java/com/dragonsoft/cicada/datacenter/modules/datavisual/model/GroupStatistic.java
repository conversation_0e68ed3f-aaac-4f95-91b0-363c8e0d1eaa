package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

/**
 * 分组统计配置类
 * 用于配置表格分组统计的字段和聚合函数
 */
public class GroupStatistic {
    
    /**
     * 字段代码
     */
    private String fieldCode;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 聚合函数类型
     * 支持: sum, count, avg, max, min, std, variance
     */
    private String aggregateFunction;
    
    /**
     * 显示格式
     * 例如: #,##0.00 用于数值格式化
     */
    private String displayFormat;
    
    /**
     * 是否显示在小计行
     */
    private boolean showInSubtotal = true;
    
    /**
     * 是否显示在总计行
     */
    private boolean showInGrandTotal = true;

    public GroupStatistic() {
    }

    public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.aggregateFunction = aggregateFunction;
    }

    public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction, String displayFormat) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.aggregateFunction = aggregateFunction;
        this.displayFormat = displayFormat;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getAggregateFunction() {
        return aggregateFunction;
    }

    public void setAggregateFunction(String aggregateFunction) {
        this.aggregateFunction = aggregateFunction;
    }

    public String getDisplayFormat() {
        return displayFormat;
    }

    public void setDisplayFormat(String displayFormat) {
        this.displayFormat = displayFormat;
    }

    public boolean isShowInSubtotal() {
        return showInSubtotal;
    }

    public void setShowInSubtotal(boolean showInSubtotal) {
        this.showInSubtotal = showInSubtotal;
    }

    public boolean isShowInGrandTotal() {
        return showInGrandTotal;
    }

    public void setShowInGrandTotal(boolean showInGrandTotal) {
        this.showInGrandTotal = showInGrandTotal;
    }

    @Override
    public String toString() {
        return "GroupStatistic{" +
                "fieldCode='" + fieldCode + '\'' +
                ", fieldName='" + fieldName + '\'' +
                ", aggregateFunction='" + aggregateFunction + '\'' +
                ", displayFormat='" + displayFormat + '\'' +
                ", showInSubtotal=" + showInSubtotal +
                ", showInGrandTotal=" + showInGrandTotal +
                '}';
    }
}
