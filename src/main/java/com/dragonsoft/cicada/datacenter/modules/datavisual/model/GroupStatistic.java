package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

/**
 * Group statistics configuration class
 * Used to configure fields and aggregate functions for table group statistics
 * 
 * <AUTHOR> Generated
 */
public class GroupStatistic {
    
    /**
     * Field code
     */
    private String fieldCode;
    
    /**
     * Field name
     */
    private String fieldName;
    
    /**
     * Aggregate function type
     * Supported: sum, count, avg, max, min, std, variance
     */
    private String aggregateFunction;
    
    /**
     * Display format
     * Example: #,##0.00 for number formatting
     */
    private String displayFormat;
    
    /**
     * Whether to show in subtotal row
     */
    private boolean showInSubtotal = true;
    
    /**
     * Whether to show in grand total row
     */
    private boolean showInGrandTotal = true;

    /**
     * Default constructor
     */
    public GroupStatistic() {
    }

    /**
     * Basic constructor
     * 
     * @param fieldCode Field code
     * @param fieldName Field name
     * @param aggregateFunction Aggregate function
     */
    public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.aggregateFunction = aggregateFunction;
    }

    /**
     * Full constructor
     * 
     * @param fieldCode Field code
     * @param fieldName Field name
     * @param aggregateFunction Aggregate function
     * @param displayFormat Display format
     */
    public GroupStatistic(String fieldCode, String fieldName, String aggregateFunction, String displayFormat) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.aggregateFunction = aggregateFunction;
        this.displayFormat = displayFormat;
    }

    /**
     * Get field code
     * 
     * @return Field code
     */
    public String getFieldCode() {
        return fieldCode;
    }

    /**
     * Set field code
     * 
     * @param fieldCode Field code
     */
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    /**
     * Get field name
     * 
     * @return Field name
     */
    public String getFieldName() {
        return fieldName;
    }

    /**
     * Set field name
     * 
     * @param fieldName Field name
     */
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    /**
     * Get aggregate function
     * 
     * @return Aggregate function
     */
    public String getAggregateFunction() {
        return aggregateFunction;
    }

    /**
     * Set aggregate function
     * 
     * @param aggregateFunction Aggregate function
     */
    public void setAggregateFunction(String aggregateFunction) {
        this.aggregateFunction = aggregateFunction;
    }

    /**
     * Get display format
     * 
     * @return Display format
     */
    public String getDisplayFormat() {
        return displayFormat;
    }

    /**
     * Set display format
     * 
     * @param displayFormat Display format
     */
    public void setDisplayFormat(String displayFormat) {
        this.displayFormat = displayFormat;
    }

    /**
     * Whether to show in subtotal row
     * 
     * @return true to show, false to hide
     */
    public boolean isShowInSubtotal() {
        return showInSubtotal;
    }

    /**
     * Set whether to show in subtotal row
     * 
     * @param showInSubtotal true to show, false to hide
     */
    public void setShowInSubtotal(boolean showInSubtotal) {
        this.showInSubtotal = showInSubtotal;
    }

    /**
     * Whether to show in grand total row
     * 
     * @return true to show, false to hide
     */
    public boolean isShowInGrandTotal() {
        return showInGrandTotal;
    }

    /**
     * Set whether to show in grand total row
     * 
     * @param showInGrandTotal true to show, false to hide
     */
    public void setShowInGrandTotal(boolean showInGrandTotal) {
        this.showInGrandTotal = showInGrandTotal;
    }

    /**
     * Validate if configuration is valid
     * 
     * @return Whether configuration is valid
     */
    public boolean isValid() {
        return fieldCode != null && !fieldCode.trim().isEmpty() &&
               fieldName != null && !fieldName.trim().isEmpty() &&
               aggregateFunction != null && !aggregateFunction.trim().isEmpty();
    }

    /**
     * Get unique key for the statistic field
     * Used to identify this statistic field in result data
     * 
     * @return Unique key for the statistic field
     */
    public String getStatisticKey() {
        return fieldCode + "_" + aggregateFunction;
    }

    /**
     * Get display name
     * Contains field name and aggregate function display name
     * 
     * @return Display name
     */
    public String getDisplayName() {
        return fieldName + "(" + getAggregateFunctionDisplayName() + ")";
    }

    /**
     * Get display name for aggregate function
     * 
     * @return Display name for aggregate function
     */
    public String getAggregateFunctionDisplayName() {
        if (aggregateFunction == null) {
            return "";
        }
        switch (aggregateFunction.toLowerCase()) {
            case "sum":
                return "Sum";
            case "count":
                return "Count";
            case "avg":
                return "Average";
            case "max":
                return "Maximum";
            case "min":
                return "Minimum";
            case "std":
                return "Standard Deviation";
            case "variance":
                return "Variance";
            default:
                return aggregateFunction;
        }
    }

    /**
     * Copy current configuration
     * 
     * @return Copy of current configuration
     */
    public GroupStatistic copy() {
        GroupStatistic copy = new GroupStatistic(this.fieldCode, this.fieldName, this.aggregateFunction, this.displayFormat);
        copy.setShowInSubtotal(this.showInSubtotal);
        copy.setShowInGrandTotal(this.showInGrandTotal);
        return copy;
    }

    @Override
    public String toString() {
        return "GroupStatistic{" +
                "fieldCode='" + fieldCode + '\'' +
                ", fieldName='" + fieldName + '\'' +
                ", aggregateFunction='" + aggregateFunction + '\'' +
                ", displayFormat='" + displayFormat + '\'' +
                ", showInSubtotal=" + showInSubtotal +
                ", showInGrandTotal=" + showInGrandTotal +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GroupStatistic that = (GroupStatistic) o;

        if (showInSubtotal != that.showInSubtotal) return false;
        if (showInGrandTotal != that.showInGrandTotal) return false;
        if (fieldCode != null ? !fieldCode.equals(that.fieldCode) : that.fieldCode != null) return false;
        if (fieldName != null ? !fieldName.equals(that.fieldName) : that.fieldName != null) return false;
        if (aggregateFunction != null ? !aggregateFunction.equals(that.aggregateFunction) : that.aggregateFunction != null)
            return false;
        return displayFormat != null ? displayFormat.equals(that.displayFormat) : that.displayFormat == null;
    }

    @Override
    public int hashCode() {
        int result = fieldCode != null ? fieldCode.hashCode() : 0;
        result = 31 * result + (fieldName != null ? fieldName.hashCode() : 0);
        result = 31 * result + (aggregateFunction != null ? aggregateFunction.hashCode() : 0);
        result = 31 * result + (displayFormat != null ? displayFormat.hashCode() : 0);
        result = 31 * result + (showInSubtotal ? 1 : 0);
        result = 31 * result + (showInGrandTotal ? 1 : 0);
        return result;
    }
}
