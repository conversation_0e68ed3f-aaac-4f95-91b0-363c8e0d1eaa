package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.dragonsoft.dataquery.service.querymodel.ParamDataModel;
import com.code.meta.dml.standard.StandardQuery;
import com.code.meta.dml.standard.cdins.QueryCdins;
import com.code.meta.dml.standard.cdins.Select;
import com.code.meta.dml.standard.cdins.SelectCdin;
import com.code.meta.dml.standard.cdins.Order;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.code.metadata.res.ddl.LogicDataObj;
import com.dragonsoft.cicada.datacenter.modules.datavisual.dataset.IDataSetBuilder;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 表格组件模型
 * 负责将查询结果构建为前端表格所需的标准结构。
 */
@Slf4j
@WidgetLabel(name = "表格", type = WidgetType.TABLE_CHART, describe = "普通明细表格")
public class TableStaticWidget extends AbsChartWidget {

    @Override
    public Map<String, Object> loadingData(IDataSetBuilder dataSetBuilder, int mode, String code, int timers) {
        log.info("TableStaticWidget loadingData called with mode: {}, code: {}", mode, code);
        
        String newQuery = getSQL(dataSetBuilder, mode);
        ColumnDataModel columns = null;
        
        try {
            ParamDataModel paramDataModel = new ParamDataModel();
            paramDataModel.setScript(newQuery);
            LogicDataObj obj = logicDataObjService.findLogicDataObjById(this.getWidgetDataset().getClassifierStatId());
            
            if (!"QUICK_SQL".equalsIgnoreCase(obj.getBelongType())) {
                columns = queryDataService.queryData(obj.getId(), paramDataModel);
            } else {
                LogicDataObj metaLogicDataObJ = logicDataObjService.getMetaLogicDataObJ(obj);
                columns = queryDataService.queryData(metaLogicDataObJ.getId(), paramDataModel);
            }
        } catch (Exception e) {
            log.error("Error loading data for TableStaticWidget", e);
            Map<String, Object> errorResponse = Maps.newHashMap();
            errorResponse.put("code", -1);
            errorResponse.put("message", "数据加载失败: " + e.getMessage());
            return errorResponse;
        }
        
        return builderResult(columns, code);
    }

    @Override
    public String getSQL(IDataSetBuilder dataSetBuilder, int mode) {
        log.info("TableStaticWidget getSQL called with mode: {}", mode);
        
        StandardQuery query = new StandardQuery(this.widgetDataset.getDbType());
        if (this.getWidgetDataset().getDbType() != null && this.getWidgetDataset().getDbType().toLowerCase().equals(DB_TYPE_ES)) {
            query.setSqlTYpe(StandardQuery.type.ES);
        }
        
        QueryCdins queryCdins = new QueryCdins(this.getSearchSQL(this.widgetDataset.getClassifierStatId()));
        query.setFromCdin(queryCdins);
        
        List<Select> selects = new ArrayList<>();
        
        // Add dimension fields
        for (WidgetDatasetDims dim : this.widgetDataset.getWidgetDatasetDims()) {
            selects.add(new Select(dim.getFiledCode(), dim.getFiledAlias(), "1".equals(dim.getDistinct())));
        }
        
        // Add measure fields
        for (WidgetDatasetMeasures measure : this.widgetDataset.getWidgetDatasetMeasures()) {
            selects.add(new Select(measure.getFiledCode(), measure.getFiledAlias(), "1".equals(measure.getIsDistinct())));
        }
        
        query.setSelectCdin(new SelectCdin(selects.toArray(new Select[selects.size()])));
        query.setConditions(this.getCondition(queryCdins));
        
        // Add ordering if needed
        Order[] orders = this.getOrders(this.widgetDataset.getFromListDims(), this.widgetDataset.getFromListMeasures());
        if (orders != null) {
            query.setOrderCdins(orders);
        }
        
        if (Objects.equals(this.widgetDataset.getDbType().toUpperCase(), "MYSQL")) {
            return query.toExpression().getScript().replaceAll("`", "");
        }
        return query.toExpression().getScript().replaceAll("`", "\"");
    }

    /**
     * 构建表格组件所需的数据结构。
     *
     * @param columnDataModel 从数据查询服务获取的原始列式数据。
     * @param code            前端可能传入的特定编码，用于区分不同类型的表格。
     * @return 一个包含 columns (列定义) 和 rows (行数据) 的 Map。
     */
    public Map<String, Object> builderResult(ColumnDataModel columnDataModel, String code) {
        Map<String, Object> response = Maps.newHashMap();
        TableData tableData = new TableData();

        // 1. 构建列定义 (Columns)
        // 将维度和度量字段合并，创建前端表格所需的列头定义。
        // 这种结构对前端非常友好，例如 antd 的 Table columns 属性。
        List<Map<String, String>> columnDefinitions = Stream.concat(
                // 维度列
                this.widgetDataset.getWidgetDatasetDims().stream()
                        .map(dim -> {
                            Map<String, String> col = Maps.newLinkedHashMap();
                            col.put("key", dim.getFiledCode());    // 字段的唯一标识
                            col.put("dataIndex", dim.getFiledCode()); // antd 等组件需要
                            col.put("title", dim.getFiledName());  // 表格中显示的列名
                            return col;
                        }),
                // 度量列
                this.widgetDataset.getWidgetDatasetMeasures().stream()
                        .map(measure -> {
                            Map<String, String> col = Maps.newLinkedHashMap();
                            col.put("key", measure.getFiledCode());
                            col.put("dataIndex", measure.getFiledCode());
                            col.put("title", measure.getFiledName());
                            return col;
                        })
        ).collect(Collectors.toList());

        tableData.setColumns(columnDefinitions);

        // 2. 设置行数据 (Rows)
        // 对于标准表格，最直接的方式就是使用查询返回的原始明细数据。
        // getFieldValue() 返回的 List<Map> 已经是理想的行数据格式。
        if (columnDataModel != null && columnDataModel.getFieldValue() != null) {
            List<Map<String, Object>> rows = new ArrayList<>();
            for (Object row : columnDataModel.getFieldValue()) {
                if (row instanceof Map) {
                    rows.add((Map<String, Object>) row);
                }
            }
            tableData.setRows(rows);
        }

        response.put("code", 1);
        response.put("data", tableData);
        return response;
    }

    /**
     * 用于承载表格数据的标准内部类。
     */
    public static class TableData {
        private List<Map<String, String>> columns = Lists.newArrayList();
        private List<Map<String, Object>> rows = Lists.newArrayList();

        // --- Getters and Setters ---
        public List<Map<String, String>> getColumns() {
            return columns;
        }

        public void setColumns(List<Map<String, String>> columns) {
            this.columns = columns;
        }

        public List<Map<String, Object>> getRows() {
            return rows;
        }

        public void setRows(List<Map<String, Object>> rows) {
            this.rows = rows;
        }
    }
}
