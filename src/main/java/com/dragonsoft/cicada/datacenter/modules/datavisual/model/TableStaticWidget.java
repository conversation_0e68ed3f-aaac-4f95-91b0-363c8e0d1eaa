package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 表格组件模型
 * 负责将查询结果构建为前端表格所需的标准结构。
 */
@WidgetLabel(name = "表格", type = WidgetType.TABLE, describe = "普通明细表格")
public class TableStaticWidget extends AbsChartWidget { // 建议继承一个包含通用属性（如widgetDataset）的基类

    /**
     * 构建表格组件所需的数据结构。
     *
     * @param columnDataModel 从数据查询服务获取的原始列式数据。
     * @param code            前端可能传入的特定编码，用于区分不同类型的表格。
     * @return 一个包含 columns (列定义) 和 rows (行数据) 的 Map。
     */
    @Override
    public Map<String, Object> builderResult(ColumnDataModel columnDataModel, String code) {
        Map<String, Object> response = Maps.newHashMap();
        TableData tableData = new TableData();

        // 1. 构建列定义 (Columns)
        // 将维度和度量字段合并，创建前端表格所需的列头定义。
        // 这种结构对前端非常友好，例如 antd 的 Table columns 属性。
        List<Map<String, String>> columnDefinitions = Stream.concat(
                // 维度列
                this.widgetDataset.getWidgetDatasetDims().stream()
                        .map(dim -> {
                            Map<String, String> col = Maps.newLinkedHashMap();
                            col.put("key", dim.getFiledCode());    // 字段的唯一标识
                            col.put("dataIndex", dim.getFiledCode()); // antd 等组件需要
                            col.put("title", dim.getFiledName());  // 表格中显示的列名
                            return col;
                        }),
                // 度量列
                this.widgetDataset.getWidgetDatasetMeasures().stream()
                        .map(measure -> {
                            Map<String, String> col = Maps.newLinkedHashMap();
                            col.put("key", measure.getFiledCode());
                            col.put("dataIndex", measure.getFiledCode());
                            col.put("title", measure.getFiledName());
                            return col;
                        })
        ).collect(Collectors.toList());

        tableData.setColumns(columnDefinitions);

        // 2. 设置行数据 (Rows)
        // 对于标准表格，最直接的方式就是使用查询返回的原始明细数据。
        // getFieldValue() 返回的 List<Map> 已经是理想的行数据格式。
        tableData.setRows((List<Map<String,Object>>)columnDataModel.getFieldValue());

        response.put("code", 1);
        response.put("data", tableData);
        return response;
    }

    /**
     * 用于承载表格数据的标准内部类。
     */
    public static class TableData {
        private List<Map<String, String>> columns = Lists.newArrayList();
        private List<Map<String, Object>> rows = Lists.newArrayList();

        // --- Getters and Setters ---
        public List<Map<String, String>> getColumns() {
            return columns;
        }

        public void setColumns(List<Map<String, String>> columns) {
            this.columns = columns;
        }

        public List<Map<String, Object>> getRows() {
            return rows;
        }

        public void setRows(List<Map<String, Object>> rows) {
            this.rows = rows;
        }
    }
}