package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.code.metadata.datavisual.WidgetDatasetDims;
import com.code.metadata.datavisual.WidgetDatasetMeasures;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.annotation.WidgetLabel;
import com.dragonsoft.cicada.datacenter.modules.datavisual.model.enums.WidgetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@WidgetLabel(name = "表格分组统计",type = WidgetType.TABLE_GROUP_STATISTICS,describe = "支持分组统计的表格组件")
public class PieChartWidget extends BarChartWidget {
    private static final String GROUP_TABLE_TYPE = "GroupedTableWidget";
    protected TableGroupData tableGroupData = new TableGroupData();

    // 分组统计配置
    private List<String> groupByFields = new ArrayList<>();
    private List<GroupStatistic> groupStatistics = new ArrayList<>();
    private boolean showSubtotal = true;
    private boolean showGrandTotal = true;
    @Override
    public Map<String, Object> builderResult(ColumnDataModel columns,String code) {
        if(PIE_TYPE.equals(code) && this.widgetDataset.getWidgetDatasetDims().size() > 1 && this.widgetDataset.getWidgetDatasetDimsDrill().isEmpty()){
            return builderRingPieChartWidget(columns);
        }else{
            return super.builderResult(columns,code);
        }
    }

    private Map<String, Object> builderRingPieChartWidget(ColumnDataModel columns) {
        Map<String,Object> res = Maps.newHashMap();
        //维度
        Set<WidgetDatasetDims> dims = this.widgetDataset.getWidgetDatasetDims();
        List<String> resDims = Lists.newArrayList();
        for (WidgetDatasetDims dim : dims) {
            resDims.add(dim.getFiledCode());
        }
        pieChartData.columns.put("dims",resDims);
        //度量
        WidgetDatasetMeasures measures = this.widgetDataset.getWidgetDatasetMeasures().iterator().next();
        pieChartData.columns.put("measures",measures.getFiledCode());
        //计算大类的数值
        Map<String,Long> countMap = Maps.newLinkedHashMap();
        Map<String,Map<String,String>> bigTypeMap = Maps.newLinkedHashMap();
        List<WidgetDatasetDims> fromListDims = this.widgetDataset.getFromListDims();
        WidgetDatasetMeasures datasetMeasures = this.widgetDataset.getFromListMeasures().get(0);
        WidgetDatasetDims bigType = fromListDims.get(0);
        WidgetDatasetDims smallType = fromListDims.get(1);

        for (Map map : columns.getFieldValue()) {
            //计算大类的值
            String name = (String) map.get(bigType.getFiledCode());
            Long num = 0L;
            if (map.get(datasetMeasures.getFiledCode()) instanceof BigDecimal) {
                num = ((BigDecimal)map.get(datasetMeasures.getFiledCode())).longValue();
            }else{
                num= new BigDecimal(map.get(datasetMeasures.getFiledCode()).toString()).longValue();
            }
            Long count = countMap.get(name);
            if(count == null){
                count = 0L;
            }
            countMap.put(name,count + num);

            Map<String, String> smallTypeValue =  bigTypeMap.get(name);
            if(smallTypeValue == null){
                smallTypeValue = Maps.newLinkedHashMap();
            }

            smallTypeValue.put((String) map.get(smallType.getFiledCode()),num.toString());
            bigTypeMap.put(name,smallTypeValue);
        }

        for (Map.Entry<String, Long> entry : countMap.entrySet()) {
            Map<String,Object> resMap = Maps.newHashMap();
            resMap.put("name",entry.getKey());
            resMap.put("value",entry.getValue());
            resMap.put("child",bigTypeMap.get(entry.getKey()));
            pieChartData.rows.add(resMap);
        }

        List<String> dimsCode = this.widgetDataset.getFromListDims().stream().map(s->s.getFiledName()).collect(Collectors.toList());
        this.pieChartData.setDimsCodes(dimsCode);
        res.put("code",1);
        res.put("data",pieChartData);
        return res;
    }

    public class PieChartData{
        private Map<String,Object> columns = Maps.newLinkedHashMap();
        private List<Map> rows = Lists.newArrayList();
        List<String> dimsCodes = new LinkedList<>();

        public Map<String, Object> getColumns() {
            return columns;
        }

        public void setColumns(Map<String, Object> columns) {
            this.columns = columns;
        }

        public List<Map> getRows() {
            return rows;
        }

        public void setRows(List<Map> rows) {
            this.rows = rows;
        }

        public List<String> getDimsCodes() {
            return dimsCodes;
        }

        public void setDimsCodes(List<String> dimsCodes) {
            this.dimsCodes = dimsCodes;
        }
    }

    public PieChartData getPieChartData() {
        return pieChartData;
    }

    public void setPieChartData(PieChartData pieChartData) {
        this.pieChartData = pieChartData;
    }
}
