package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import com.code.dragonsoft.dataquery.service.querymodel.ColumnDataModel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TableGroupStatisticsWidget 测试类
 * 
 * <AUTHOR> Generated
 */
public class TableGroupStatisticsWidgetTest {

    private TableGroupStatisticsWidget widget;
    private ColumnDataModel testData;

    @BeforeEach
    public void setUp() {
        widget = new TableGroupStatisticsWidget();
        testData = createTestData();
    }

    /**
     * 创建测试数据
     */
    private ColumnDataModel createTestData() {
        ColumnDataModel data = new ColumnDataModel();
        
        // 设置字段名
        List<String> fieldNames = Lists.newArrayList("department", "region", "sales_amount", "order_count");
        data.setFieldName(fieldNames);
        
        // 设置测试数据
        List<Map> fieldValues = Lists.newArrayList();
        
        // 销售部数据
        Map<String, Object> row1 = Maps.newHashMap();
        row1.put("department", "销售部");
        row1.put("region", "华东");
        row1.put("sales_amount", 100000);
        row1.put("order_count", 50);
        fieldValues.add(row1);
        
        Map<String, Object> row2 = Maps.newHashMap();
        row2.put("department", "销售部");
        row2.put("region", "华南");
        row2.put("sales_amount", 80000);
        row2.put("order_count", 40);
        fieldValues.add(row2);
        
        // 市场部数据
        Map<String, Object> row3 = Maps.newHashMap();
        row3.put("department", "市场部");
        row3.put("region", "华东");
        row3.put("sales_amount", 60000);
        row3.put("order_count", 30);
        fieldValues.add(row3);
        
        Map<String, Object> row4 = Maps.newHashMap();
        row4.put("department", "市场部");
        row4.put("region", "华南");
        row4.put("sales_amount", 70000);
        row4.put("order_count", 35);
        fieldValues.add(row4);
        
        data.setFieldValue(fieldValues);
        return data;
    }

    @Test
    public void testBasicGrouping() {
        // 配置分组字段
        widget.addGroupByField("department");
        
        // 配置统计字段
        widget.addStatistic("sales_amount", "销售额", "sum");
        widget.addStatistic("order_count", "订单数", "count");
        
        // 设置显示选项
        widget.setDisplayOptions(false, true);
        
        // 验证配置
        assertEquals(1, widget.getGroupByFields().size());
        assertEquals("department", widget.getGroupByFields().get(0));
        assertEquals(2, widget.getGroupStatistics().size());
        assertTrue(widget.isShowGrandTotal());
        assertFalse(widget.isShowSubtotal());
    }

    @Test
    public void testMultiFieldGrouping() {
        // 配置多个分组字段
        widget.addGroupByField("department");
        widget.addGroupByField("region");
        
        // 配置统计字段
        widget.addStatistic("sales_amount", "sum");
        widget.addStatistic("order_count", "avg");
        
        // 验证配置
        assertEquals(2, widget.getGroupByFields().size());
        assertEquals(2, widget.getGroupStatistics().size());
    }

    @Test
    public void testClearConfiguration() {
        // 添加配置
        widget.addGroupByField("department");
        widget.addStatistic("sales_amount", "sum");
        
        // 验证配置已添加
        assertEquals(1, widget.getGroupByFields().size());
        assertEquals(1, widget.getGroupStatistics().size());
        
        // 清空配置
        widget.clearGroupConfiguration();
        
        // 验证配置已清空
        assertEquals(0, widget.getGroupByFields().size());
        assertEquals(0, widget.getGroupStatistics().size());
    }

    @Test
    public void testDisplayOptions() {
        // 测试默认值
        assertTrue(widget.isShowSubtotal());
        assertTrue(widget.isShowGrandTotal());
        
        // 设置显示选项
        widget.setDisplayOptions(false, true);
        assertFalse(widget.isShowSubtotal());
        assertTrue(widget.isShowGrandTotal());
        
        // 单独设置
        widget.setShowSubtotal(true);
        widget.setShowGrandTotal(false);
        assertTrue(widget.isShowSubtotal());
        assertFalse(widget.isShowGrandTotal());
    }

    @Test
    public void testDataStructure() {
        TableGroupStatisticsWidget.TableGroupStatisticsData data = new TableGroupStatisticsWidget.TableGroupStatisticsData();
        
        // 测试初始状态
        assertNotNull(data.getColumns());
        assertNotNull(data.getRows());
        assertNotNull(data.getDimsCodes());
        assertNotNull(data.getGroupConfig());
        
        assertTrue(data.getColumns().isEmpty());
        assertTrue(data.getRows().isEmpty());
        assertTrue(data.getDimsCodes().isEmpty());
        assertTrue(data.getGroupConfig().isEmpty());
        
        // 测试设置数据
        Map<String, Object> columns = Maps.newHashMap();
        columns.put("dept", "部门");
        data.setColumns(columns);
        assertEquals(1, data.getColumns().size());
        
        List<Map<String, Object>> rows = Lists.newArrayList();
        Map<String, Object> row = Maps.newHashMap();
        row.put("dept", "销售部");
        rows.add(row);
        data.setRows(rows);
        assertEquals(1, data.getRows().size());
        
        List<String> dimsCodes = Lists.newArrayList("部门");
        data.setDimsCodes(dimsCodes);
        assertEquals(1, data.getDimsCodes().size());
        
        Map<String, Object> groupConfig = Maps.newHashMap();
        groupConfig.put("groupBy", "department");
        data.setGroupConfig(groupConfig);
        assertEquals(1, data.getGroupConfig().size());
    }

    @Test
    public void testAddStatisticMethods() {
        // 测试完整参数方法
        widget.addStatistic("sales_amount", "销售额", "sum");
        assertEquals(1, widget.getGroupStatistics().size());
        
        GroupStatistic stat1 = widget.getGroupStatistics().get(0);
        assertEquals("sales_amount", stat1.getFieldCode());
        assertEquals("销售额", stat1.getFieldName());
        assertEquals("sum", stat1.getAggregateFunction());
        
        // 测试简化方法
        widget.addStatistic("order_count", "count");
        assertEquals(2, widget.getGroupStatistics().size());
        
        GroupStatistic stat2 = widget.getGroupStatistics().get(1);
        assertEquals("order_count", stat2.getFieldCode());
        assertEquals("count", stat2.getAggregateFunction());
    }

    @Test
    public void testGroupByFieldManagement() {
        // 测试添加分组字段
        widget.addGroupByField("department");
        assertEquals(1, widget.getGroupByFields().size());
        
        // 测试重复添加（不应该重复）
        widget.addGroupByField("department");
        assertEquals(1, widget.getGroupByFields().size());
        
        // 测试添加不同字段
        widget.addGroupByField("region");
        assertEquals(2, widget.getGroupByFields().size());
        
        // 测试设置字段列表
        List<String> newFields = Lists.newArrayList("category", "subcategory");
        widget.setGroupByFields(newFields);
        assertEquals(2, widget.getGroupByFields().size());
        assertEquals("category", widget.getGroupByFields().get(0));
        assertEquals("subcategory", widget.getGroupByFields().get(1));
    }

    @Test
    public void testGettersAndSetters() {
        // 测试 TableGroupStatisticsData
        TableGroupStatisticsWidget.TableGroupStatisticsData data = new TableGroupStatisticsWidget.TableGroupStatisticsData();
        widget.setTableGroupStatisticsData(data);
        assertSame(data, widget.getTableGroupStatisticsData());
        
        // 测试 GroupStatistics
        List<GroupStatistic> statistics = Lists.newArrayList();
        statistics.add(new GroupStatistic("field1", "Field 1", "sum"));
        widget.setGroupStatistics(statistics);
        assertSame(statistics, widget.getGroupStatistics());
        
        // 测试 GroupByFields
        List<String> fields = Lists.newArrayList("field1", "field2");
        widget.setGroupByFields(fields);
        assertSame(fields, widget.getGroupByFields());
    }
}
