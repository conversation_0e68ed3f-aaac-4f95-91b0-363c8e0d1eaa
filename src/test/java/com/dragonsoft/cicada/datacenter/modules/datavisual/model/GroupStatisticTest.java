package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GroupStatistic 测试类
 * 
 * <AUTHOR> Generated
 */
public class GroupStatisticTest {

    private GroupStatistic groupStatistic;

    @BeforeEach
    public void setUp() {
        groupStatistic = new GroupStatistic();
    }

    @Test
    public void testDefaultConstructor() {
        GroupStatistic stat = new GroupStatistic();
        
        assertNull(stat.getFieldCode());
        assertNull(stat.getFieldName());
        assertNull(stat.getAggregateFunction());
        assertNull(stat.getDisplayFormat());
        assertTrue(stat.isShowInSubtotal());
        assertTrue(stat.isShowInGrandTotal());
    }

    @Test
    public void testBasicConstructor() {
        GroupStatistic stat = new GroupStatistic("sales_amount", "销售额", "sum");
        
        assertEquals("sales_amount", stat.getFieldCode());
        assertEquals("销售额", stat.getFieldName());
        assertEquals("sum", stat.getAggregateFunction());
        assertNull(stat.getDisplayFormat());
        assertTrue(stat.isShowInSubtotal());
        assertTrue(stat.isShowInGrandTotal());
    }

    @Test
    public void testFullConstructor() {
        GroupStatistic stat = new GroupStatistic("sales_amount", "销售额", "sum", "#,##0.00");
        
        assertEquals("sales_amount", stat.getFieldCode());
        assertEquals("销售额", stat.getFieldName());
        assertEquals("sum", stat.getAggregateFunction());
        assertEquals("#,##0.00", stat.getDisplayFormat());
        assertTrue(stat.isShowInSubtotal());
        assertTrue(stat.isShowInGrandTotal());
    }

    @Test
    public void testGettersAndSetters() {
        // 测试字段代码
        groupStatistic.setFieldCode("test_field");
        assertEquals("test_field", groupStatistic.getFieldCode());
        
        // 测试字段名称
        groupStatistic.setFieldName("测试字段");
        assertEquals("测试字段", groupStatistic.getFieldName());
        
        // 测试聚合函数
        groupStatistic.setAggregateFunction("avg");
        assertEquals("avg", groupStatistic.getAggregateFunction());
        
        // 测试显示格式
        groupStatistic.setDisplayFormat("#,##0.00");
        assertEquals("#,##0.00", groupStatistic.getDisplayFormat());
        
        // 测试小计显示
        groupStatistic.setShowInSubtotal(false);
        assertFalse(groupStatistic.isShowInSubtotal());
        
        // 测试总计显示
        groupStatistic.setShowInGrandTotal(false);
        assertFalse(groupStatistic.isShowInGrandTotal());
    }

    @Test
    public void testIsValid() {
        // 测试无效配置
        assertFalse(groupStatistic.isValid());
        
        // 设置字段代码
        groupStatistic.setFieldCode("sales_amount");
        assertFalse(groupStatistic.isValid());
        
        // 设置字段名称
        groupStatistic.setFieldName("销售额");
        assertFalse(groupStatistic.isValid());
        
        // 设置聚合函数
        groupStatistic.setAggregateFunction("sum");
        assertTrue(groupStatistic.isValid());
        
        // 测试空字符串
        groupStatistic.setFieldCode("");
        assertFalse(groupStatistic.isValid());
        
        groupStatistic.setFieldCode("   ");
        assertFalse(groupStatistic.isValid());
        
        // 恢复有效值
        groupStatistic.setFieldCode("sales_amount");
        assertTrue(groupStatistic.isValid());
    }

    @Test
    public void testGetStatisticKey() {
        groupStatistic.setFieldCode("sales_amount");
        groupStatistic.setAggregateFunction("sum");
        
        assertEquals("sales_amount_sum", groupStatistic.getStatisticKey());
        
        groupStatistic.setAggregateFunction("avg");
        assertEquals("sales_amount_avg", groupStatistic.getStatisticKey());
    }

    @Test
    public void testGetDisplayName() {
        groupStatistic.setFieldName("销售额");
        groupStatistic.setAggregateFunction("sum");
        
        assertEquals("销售额(求和)", groupStatistic.getDisplayName());
        
        groupStatistic.setAggregateFunction("count");
        assertEquals("销售额(计数)", groupStatistic.getDisplayName());
    }

    @Test
    public void testGetAggregateFunctionDisplayName() {
        // 测试所有支持的聚合函数
        groupStatistic.setAggregateFunction("sum");
        assertEquals("求和", groupStatistic.getAggregateFunctionDisplayName());
        
        groupStatistic.setAggregateFunction("count");
        assertEquals("计数", groupStatistic.getAggregateFunctionDisplayName());
        
        groupStatistic.setAggregateFunction("avg");
        assertEquals("平均值", groupStatistic.getAggregateFunctionDisplayName());
        
        groupStatistic.setAggregateFunction("max");
        assertEquals("最大值", groupStatistic.getAggregateFunctionDisplayName());
        
        groupStatistic.setAggregateFunction("min");
        assertEquals("最小值", groupStatistic.getAggregateFunctionDisplayName());
        
        groupStatistic.setAggregateFunction("std");
        assertEquals("标准差", groupStatistic.getAggregateFunctionDisplayName());
        
        groupStatistic.setAggregateFunction("variance");
        assertEquals("方差", groupStatistic.getAggregateFunctionDisplayName());
        
        // 测试未知函数
        groupStatistic.setAggregateFunction("unknown");
        assertEquals("unknown", groupStatistic.getAggregateFunctionDisplayName());
        
        // 测试大小写不敏感
        groupStatistic.setAggregateFunction("SUM");
        assertEquals("求和", groupStatistic.getAggregateFunctionDisplayName());
        
        // 测试null值
        groupStatistic.setAggregateFunction(null);
        assertEquals("", groupStatistic.getAggregateFunctionDisplayName());
    }

    @Test
    public void testCopy() {
        // 设置原始对象的所有属性
        groupStatistic.setFieldCode("sales_amount");
        groupStatistic.setFieldName("销售额");
        groupStatistic.setAggregateFunction("sum");
        groupStatistic.setDisplayFormat("#,##0.00");
        groupStatistic.setShowInSubtotal(false);
        groupStatistic.setShowInGrandTotal(true);
        
        // 复制对象
        GroupStatistic copy = groupStatistic.copy();
        
        // 验证复制的对象不是同一个实例
        assertNotSame(groupStatistic, copy);
        
        // 验证所有属性都被正确复制
        assertEquals(groupStatistic.getFieldCode(), copy.getFieldCode());
        assertEquals(groupStatistic.getFieldName(), copy.getFieldName());
        assertEquals(groupStatistic.getAggregateFunction(), copy.getAggregateFunction());
        assertEquals(groupStatistic.getDisplayFormat(), copy.getDisplayFormat());
        assertEquals(groupStatistic.isShowInSubtotal(), copy.isShowInSubtotal());
        assertEquals(groupStatistic.isShowInGrandTotal(), copy.isShowInGrandTotal());
        
        // 验证修改复制对象不会影响原对象
        copy.setFieldCode("modified_field");
        assertNotEquals(groupStatistic.getFieldCode(), copy.getFieldCode());
    }

    @Test
    public void testToString() {
        groupStatistic.setFieldCode("sales_amount");
        groupStatistic.setFieldName("销售额");
        groupStatistic.setAggregateFunction("sum");
        groupStatistic.setDisplayFormat("#,##0.00");
        groupStatistic.setShowInSubtotal(false);
        groupStatistic.setShowInGrandTotal(true);
        
        String toString = groupStatistic.toString();
        
        assertTrue(toString.contains("sales_amount"));
        assertTrue(toString.contains("销售额"));
        assertTrue(toString.contains("sum"));
        assertTrue(toString.contains("#,##0.00"));
        assertTrue(toString.contains("showInSubtotal=false"));
        assertTrue(toString.contains("showInGrandTotal=true"));
    }

    @Test
    public void testEquals() {
        GroupStatistic stat1 = new GroupStatistic("sales_amount", "销售额", "sum", "#,##0.00");
        stat1.setShowInSubtotal(false);
        stat1.setShowInGrandTotal(true);
        
        GroupStatistic stat2 = new GroupStatistic("sales_amount", "销售额", "sum", "#,##0.00");
        stat2.setShowInSubtotal(false);
        stat2.setShowInGrandTotal(true);
        
        GroupStatistic stat3 = new GroupStatistic("order_count", "订单数", "count");
        
        // 测试相等
        assertEquals(stat1, stat2);
        assertEquals(stat1.hashCode(), stat2.hashCode());
        
        // 测试不相等
        assertNotEquals(stat1, stat3);
        
        // 测试与null比较
        assertNotEquals(stat1, null);
        
        // 测试与不同类型比较
        assertNotEquals(stat1, "string");
        
        // 测试自己与自己比较
        assertEquals(stat1, stat1);
    }

    @Test
    public void testHashCode() {
        GroupStatistic stat1 = new GroupStatistic("sales_amount", "销售额", "sum");
        GroupStatistic stat2 = new GroupStatistic("sales_amount", "销售额", "sum");
        
        // 相等的对象应该有相同的hashCode
        assertEquals(stat1.hashCode(), stat2.hashCode());
        
        // 修改一个属性后hashCode应该不同
        stat2.setAggregateFunction("avg");
        assertNotEquals(stat1.hashCode(), stat2.hashCode());
    }

    @Test
    public void testEdgeCases() {
        // 测试null值处理
        GroupStatistic stat = new GroupStatistic(null, null, null, null);
        assertFalse(stat.isValid());
        assertEquals("null_null", stat.getStatisticKey());
        assertEquals("null()", stat.getDisplayName());
        
        // 测试空字符串
        stat = new GroupStatistic("", "", "", "");
        assertFalse(stat.isValid());
        assertEquals("_", stat.getStatisticKey());
        assertEquals("()", stat.getDisplayName());
    }
}
