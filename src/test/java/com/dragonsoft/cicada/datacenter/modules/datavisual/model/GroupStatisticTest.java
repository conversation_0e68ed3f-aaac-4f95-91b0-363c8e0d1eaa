package com.dragonsoft.cicada.datacenter.modules.datavisual.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for GroupStatistic
 */
public class GroupStatisticTest {

    @Test
    public void testDefaultConstructor() {
        GroupStatistic statistic = new GroupStatistic();
        assertNull(statistic.getFieldCode());
        assertNull(statistic.getFieldName());
        assertNull(statistic.getAggregateFunction());
        assertNull(statistic.getDisplayFormat());
        assertTrue(statistic.isShowInSubtotal());
        assertTrue(statistic.isShowInGrandTotal());
    }

    @Test
    public void testBasicConstructor() {
        GroupStatistic statistic = new GroupStatistic("code1", "Field Name", "sum");
        assertEquals("code1", statistic.getFieldCode());
        assertEquals("Field Name", statistic.getFieldName());
        assertEquals("sum", statistic.getAggregateFunction());
        assertNull(statistic.getDisplayFormat());
        assertTrue(statistic.isShowInSubtotal());
        assertTrue(statistic.isShowInGrandTotal());
    }

    @Test
    public void testFullConstructor() {
        GroupStatistic statistic = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        assertEquals("code1", statistic.getFieldCode());
        assertEquals("Field Name", statistic.getFieldName());
        assertEquals("sum", statistic.getAggregateFunction());
        assertEquals("#,##0.00", statistic.getDisplayFormat());
        assertTrue(statistic.isShowInSubtotal());
        assertTrue(statistic.isShowInGrandTotal());
    }

    @Test
    public void testGettersAndSetters() {
        GroupStatistic statistic = new GroupStatistic();
        
        statistic.setFieldCode("test_code");
        assertEquals("test_code", statistic.getFieldCode());
        
        statistic.setFieldName("Test Field");
        assertEquals("Test Field", statistic.getFieldName());
        
        statistic.setAggregateFunction("avg");
        assertEquals("avg", statistic.getAggregateFunction());
        
        statistic.setDisplayFormat("0.00%");
        assertEquals("0.00%", statistic.getDisplayFormat());
        
        statistic.setShowInSubtotal(false);
        assertFalse(statistic.isShowInSubtotal());
        
        statistic.setShowInGrandTotal(false);
        assertFalse(statistic.isShowInGrandTotal());
    }

    @Test
    public void testIsValid() {
        GroupStatistic statistic = new GroupStatistic();
        assertFalse(statistic.isValid());
        
        statistic.setFieldCode("code1");
        assertFalse(statistic.isValid());
        
        statistic.setFieldName("Field Name");
        assertFalse(statistic.isValid());
        
        statistic.setAggregateFunction("sum");
        assertTrue(statistic.isValid());
        
        // Test with empty strings
        statistic.setFieldCode("");
        assertFalse(statistic.isValid());
        
        statistic.setFieldCode("  ");
        assertFalse(statistic.isValid());
    }

    @Test
    public void testGetStatisticKey() {
        GroupStatistic statistic = new GroupStatistic("amount", "Amount", "sum");
        assertEquals("amount_sum", statistic.getStatisticKey());
    }

    @Test
    public void testGetDisplayName() {
        GroupStatistic statistic = new GroupStatistic("amount", "Amount", "sum");
        assertEquals("Amount(Sum)", statistic.getDisplayName());
    }

    @Test
    public void testGetAggregateFunctionDisplayName() {
        GroupStatistic statistic = new GroupStatistic();
        
        statistic.setAggregateFunction("sum");
        assertEquals("Sum", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("count");
        assertEquals("Count", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("avg");
        assertEquals("Average", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("max");
        assertEquals("Maximum", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("min");
        assertEquals("Minimum", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("std");
        assertEquals("Standard Deviation", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("variance");
        assertEquals("Variance", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction("unknown");
        assertEquals("unknown", statistic.getAggregateFunctionDisplayName());
        
        statistic.setAggregateFunction(null);
        assertEquals("", statistic.getAggregateFunctionDisplayName());
    }

    @Test
    public void testCopy() {
        GroupStatistic original = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        original.setShowInSubtotal(false);
        original.setShowInGrandTotal(false);
        
        GroupStatistic copy = original.copy();
        
        assertEquals(original.getFieldCode(), copy.getFieldCode());
        assertEquals(original.getFieldName(), copy.getFieldName());
        assertEquals(original.getAggregateFunction(), copy.getAggregateFunction());
        assertEquals(original.getDisplayFormat(), copy.getDisplayFormat());
        assertEquals(original.isShowInSubtotal(), copy.isShowInSubtotal());
        assertEquals(original.isShowInGrandTotal(), copy.isShowInGrandTotal());
        
        // Verify it's a different object
        assertNotSame(original, copy);
        
        // Verify changes to copy don't affect original
        copy.setFieldCode("modified");
        assertNotEquals(original.getFieldCode(), copy.getFieldCode());
    }

    @Test
    public void testEquals() {
        GroupStatistic stat1 = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        GroupStatistic stat2 = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        GroupStatistic stat3 = new GroupStatistic("code2", "Field Name", "sum", "#,##0.00");
        
        assertEquals(stat1, stat2);
        assertNotEquals(stat1, stat3);
        assertNotEquals(stat1, null);
        assertNotEquals(stat1, "string");
    }

    @Test
    public void testHashCode() {
        GroupStatistic stat1 = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        GroupStatistic stat2 = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        
        assertEquals(stat1.hashCode(), stat2.hashCode());
    }

    @Test
    public void testToString() {
        GroupStatistic statistic = new GroupStatistic("code1", "Field Name", "sum", "#,##0.00");
        String toString = statistic.toString();
        
        assertTrue(toString.contains("code1"));
        assertTrue(toString.contains("Field Name"));
        assertTrue(toString.contains("sum"));
        assertTrue(toString.contains("#,##0.00"));
        assertTrue(toString.contains("GroupStatistic"));
    }
}
